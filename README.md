# عارض جهات الاتصال من ملف VCF

تطبيق ويب لعرض وإدارة جهات الاتصال من ملفات VCF مع دعم كامل للنصوص العربية.

## المميزات

### 🔧 إصلاح الترميز العربي
- **إصلاح تلقائي**: يكتشف ويصلح مشاكل ترميز النصوص العربية تلقائياً
- **دعم Quoted-Printable**: يفك ترميز النصوص المُرمزة مثل `=D8=A7=D9=84=D8=A3=D9=85`
- **دعم UTF-8**: يتعامل مع جميع أنواع ترميز UTF-8
- **زر إصلاح يدوي**: زر "🔧 إصلاح الترميز" لإصلاح المشاكل يدوياً

### 🌙 الوضع الليلي/النهاري
- **تبديل تلقائي**: زر "🌙 تبديل الوضع" للتنقل بين الوضعين
- **تصميم متدرج**: خلفيات متدرجة جميلة في كلا الوضعين
- **حفظ التفضيل**: يحفظ اختيارك للجلسة القادمة

### 🔄 طرق العرض المتعددة
- **عرض البطاقات**: عرض جهات الاتصال في بطاقات منفصلة
- **عرض الجدول**: عرض جهات الاتصال في جدول منظم
- **تبديل سهل**: زر "🔄 تغيير العرض" للتنقل بين الطرق

### 📤 مشاركة متقدمة
- **رمز QR**: إنشاء رمز QR لكل جهة اتصال
- **تحميل QR**: تحميل رمز QR كصورة PNG
- **مشاركة ذكية**: مشاركة بيانات جهة الاتصال عبر Web Share API
- **نسخ للحافظة**: نسخ بيانات جهة الاتصال أو أرقام الهاتف

### 🔍 البحث والفلترة
- **بحث فوري**: البحث في الأسماء وأرقام الهاتف والبريد الإلكتروني
- **إحصائيات مباشرة**: عرض عدد جهات الاتصال ونتائج البحث

### 📊 التصدير والاستيراد
- **تصدير CSV**: تصدير جميع جهات الاتصال إلى ملف CSV
- **حفظ الجلسة**: حفظ آخر ملف تم تحميله تلقائياً
- **دعم ملفات VCF**: قراءة جميع أنواع ملفات VCF

## كيفية الاستخدام

### 1. تشغيل التطبيق
```bash
# افتح terminal في مجلد التطبيق
python -m http.server 8000

# أو استخدم أي خادم ويب آخر
# ثم افتح http://localhost:8000/main.html
```

### 2. تحميل ملف VCF
1. اضغط على زر "📤 اختر ملف VCF"
2. اختر ملف VCF من جهازك
3. سيتم عرض جهات الاتصال تلقائياً

### 3. إصلاح مشاكل الترميز
إذا ظهرت الأسماء العربية مُرمزة مثل `=D8=A7=D9=84=D8=A3=D9=85`:
1. اضغط على زر "🔧 إصلاح الترميز"
2. سيتم إصلاح الترميز تلقائياً
3. ستظهر الأسماء العربية بشكل صحيح

### 4. استخدام المميزات
- **البحث**: اكتب في مربع البحث للعثور على جهة اتصال
- **تغيير العرض**: اضغط "🔄 تغيير العرض" للتنقل بين البطاقات والجدول
- **الوضع الليلي**: اضغط "🌙 تبديل الوضع" لتغيير المظهر
- **مشاركة QR**: اضغط "📤 QR" لإنشاء رمز QR لجهة الاتصال
- **تصدير**: اضغط "⬇️ تصدير إلى CSV" لحفظ جميع جهات الاتصال

## ملف الاختبار
يتضمن التطبيق ملف `test-arabic.vcf` يحتوي على:
- جهات اتصال بترميز مُشكِل (quoted-printable)
- جهات اتصال بترميز صحيح
- أمثلة متنوعة لاختبار وظائف الإصلاح

## المتطلبات التقنية
- متصفح ويب حديث يدعم JavaScript ES6+
- دعم Web Share API (اختياري للمشاركة المتقدمة)
- دعم Clipboard API (لنسخ النصوص)

## الدعم التقني
- **الترميز**: دعم كامل للنصوص العربية وجميع أنواع الترميز
- **التوافق**: يعمل على جميع المتصفحات الحديثة
- **الاستجابة**: تصميم متجاوب يعمل على الهاتف والكمبيوتر

---

تم تطوير هذا التطبيق لحل مشاكل ترميز النصوص العربية في ملفات VCF وتوفير تجربة مستخدم متميزة.
