<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>عارض جهات الاتصال (VCF)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode/build/qrcode.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            transition: background 0.5s, color 0.5s;
        }

        .glass {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(12px);
            border-radius: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .dark-mode {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: white;
        }

        .light-mode {
            background: linear-gradient(135deg, #f9fafb 0%, #e5e7eb 100%);
            color: #111827;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: bold;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: #10b981;
        }

        .notification.error {
            background: #ef4444;
        }

        .notification.info {
            background: #3b82f6;
        }

        input[type="file"]::file-selector-button {
            background: #3b82f6;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
        }
    </style>
</head>

<body class="dark-mode min-h-screen flex flex-col items-center p-4" id="app">
    <h1 class="text-3xl font-bold mb-4">عارض جهات الاتصال من ملف VCF</h1>

    <div class="mb-4 flex flex-wrap items-center gap-4">
        <label class="cursor-pointer bg-blue-500 px-4 py-2 rounded text-white">
            📤 اختر ملف VCF
            <input type="file" id="vcfInput" accept=".vcf" hidden />
        </label>

        <button id="exportBtn" class="bg-green-500 px-4 py-2 rounded text-white">⬇️ تصدير إلى CSV</button>
        <button id="toggleMode" class="bg-yellow-500 px-4 py-2 rounded text-white">🌙 تبديل الوضع</button>
        <button id="toggleView" class="bg-purple-500 px-4 py-2 rounded text-white">🔄 تغيير العرض</button>
        <button id="fixEncoding" class="bg-red-500 px-4 py-2 rounded text-white">🔧 إصلاح الترميز</button>
    </div>

    <input type="text" id="searchInput" placeholder="🔍 ابحث باسم أو رقم..."
        class="text-black p-2 rounded mb-4 w-full max-w-md" />

    <div id="statsContainer" class="mb-4 text-center">
        <span id="contactCount" class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm">0 جهة اتصال</span>
        <span id="filteredCount" class="bg-green-600 text-white px-3 py-1 rounded-full text-sm ml-2 hidden">0 نتيجة</span>
    </div>

    <div id="contactsContainer" class="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 w-full max-w-6xl"></div>

    <div id="qrModal"
        class="fixed top-0 left-0 w-full h-full bg-black bg-opacity-70 hidden items-center justify-center z-50">
        <div class="bg-white text-black p-6 rounded-lg shadow-xl max-w-sm">
            <h3 class="text-lg font-bold mb-4 text-center">مشاركة جهة الاتصال</h3>
            <div id="qrCode" class="flex justify-center mb-4"></div>
            <div class="flex gap-2 justify-center">
                <button id="downloadQR" class="px-4 py-2 bg-blue-500 text-white rounded">⬇️ تحميل</button>
                <button id="shareQR" class="px-4 py-2 bg-green-500 text-white rounded">📤 مشاركة</button>
                <button onclick="document.getElementById('qrModal').classList.add('hidden')"
                    class="px-4 py-2 bg-red-500 text-white rounded">إغلاق</button>
            </div>
        </div>
    </div>

    <script>
        const app = document.getElementById('app');
        const vcfInput = document.getElementById('vcfInput');
        const searchInput = document.getElementById('searchInput');
        const exportBtn = document.getElementById('exportBtn');
        const toggleMode = document.getElementById('toggleMode');
        const toggleView = document.getElementById('toggleView');
        const fixEncoding = document.getElementById('fixEncoding');
        const contactsContainer = document.getElementById('contactsContainer');
        const qrModal = document.getElementById('qrModal');
        const qrCodeContainer = document.getElementById('qrCode');

        let allContacts = [];
        let viewMode = 'cards';
        let dark = true;
        let lastFileContent = '';

        // وظيفة عرض الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        // وظيفة نسخ النص إلى الحافظة
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showNotification(`تم نسخ: ${text}`, 'success');
            }).catch(() => {
                showNotification('فشل في نسخ النص', 'error');
            });
        }

        // وظيفة تحديث الإحصائيات
        function updateStats(displayedCount) {
            const contactCount = document.getElementById('contactCount');
            const filteredCount = document.getElementById('filteredCount');

            contactCount.textContent = `${allContacts.length} جهة اتصال`;

            if (displayedCount < allContacts.length) {
                filteredCount.textContent = `${displayedCount} نتيجة`;
                filteredCount.classList.remove('hidden');
            } else {
                filteredCount.classList.add('hidden');
            }
        }

        vcfInput.addEventListener('change', handleFile);
        searchInput.addEventListener('input', filterContacts);
        exportBtn.addEventListener('click', exportToCSV);
        toggleMode.addEventListener('click', () => {
            dark = !dark;
            app.className = dark ? 'dark-mode min-h-screen flex flex-col items-center p-4' : 'light-mode min-h-screen flex flex-col items-center p-4';
        });
        toggleView.addEventListener('click', () => {
            viewMode = viewMode === 'cards' ? 'table' : 'cards';
            displayContacts(allContacts);
        });
        fixEncoding.addEventListener('click', () => {
            if (lastFileContent) {
                fixEncodingIssues();
            } else {
                showNotification('يرجى تحميل ملف VCF أولاً', 'error');
            }
        });

        function decodeUTF8(input) {
            if (!input) return input;

            try {
                // إذا كان النص يحتوي على ترميز URL مثل %D8%A7
                if (input.includes('%')) {
                    return decodeURIComponent(input);
                }

                // إذا كان النص يحتوي على ترميز quoted-printable مثل =D8=A7
                if (input.includes('=') && /=[0-9A-F]{2}/i.test(input)) {
                    // تحويل من quoted-printable إلى UTF-8
                    let decoded = input.replace(/=([0-9A-F]{2})/gi, (match, hex) => {
                        return String.fromCharCode(parseInt(hex, 16));
                    });

                    // محاولة فك ترميز UTF-8
                    try {
                        return new TextDecoder('utf-8').decode(
                            new Uint8Array([...decoded].map(char => char.charCodeAt(0)))
                        );
                    } catch {
                        return decoded;
                    }
                }

                // إذا كان النص يحتوي على ترميز base64
                if (/^[A-Za-z0-9+/]+=*$/.test(input) && input.length % 4 === 0) {
                    try {
                        return atob(input);
                    } catch {
                        return input;
                    }
                }

                // محاولة فك ترميز عادي
                return decodeURIComponent(escape(input));
            } catch (e) {
                console.log('خطأ في فك الترميز:', e, 'للنص:', input);
                return input;
            }
        }

        function handleFile(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = (e) => {
                let text = e.target.result;
                lastFileContent = text; // حفظ المحتوى الأصلي

                // محاولة اكتشاف الترميز وإصلاحه
                if (text.includes('=D8=') || text.includes('=D9=')) {
                    console.log('تم اكتشاف ترميز quoted-printable');
                }

                allContacts = parseVCF(text);
                displayContacts(allContacts);
                localStorage.setItem('lastVCF', text);
                showNotification(`تم تحميل ${allContacts.length} جهة اتصال بنجاح`, 'success');
            };

            // محاولة قراءة الملف بترميزات مختلفة
            reader.readAsText(file, 'utf-8');
        }

        function fixEncodingIssues() {
            if (!lastFileContent) return;

            // محاولة إصلاح الترميز بطرق مختلفة
            let fixedContent = lastFileContent;

            // إصلاح quoted-printable encoding
            fixedContent = fixedContent.replace(/=([0-9A-F]{2})/gi, (match, hex) => {
                return String.fromCharCode(parseInt(hex, 16));
            });

            // محاولة فك ترميز UTF-8
            try {
                const bytes = new Uint8Array([...fixedContent].map(char => char.charCodeAt(0)));
                fixedContent = new TextDecoder('utf-8').decode(bytes);
            } catch (e) {
                console.log('فشل في فك ترميز UTF-8:', e);
            }

            // إعادة تحليل المحتوى المُصحح
            const newContacts = parseVCF(fixedContent);
            if (newContacts.length > allContacts.length) {
                allContacts = newContacts;
                displayContacts(allContacts);
                showNotification('تم إصلاح الترميز بنجاح!', 'success');
            } else {
                showNotification('لم يتم العثور على تحسينات في الترميز', 'info');
            }
        }

        function parseVCF(text) {
            const cards = text.split('BEGIN:VCARD').slice(1);
            return cards.map(card => {
                const lines = card.split('\n');
                const contact = {};

                lines.forEach(line => {
                    line = line.trim(); // إزالة المسافات الزائدة
                    if (line.includes(':')) {
                        const [key, ...valueParts] = line.split(':');
                        const valueRaw = valueParts.join(':'); // في حالة وجود : في القيمة نفسها

                        // فك الترميز مع تسجيل المحاولة
                        let value = decodeUTF8(valueRaw);

                        // تنظيف القيمة من الأحرف غير المرغوبة
                        if (value) {
                            value = value.replace(/\r/g, '').trim();
                        }

                        if (/FN|N:/.test(key)) {
                            contact.name = value;
                            console.log('اسم جهة الاتصال:', value);
                        }
                        if (/TEL/.test(key)) {
                            contact.phone = contact.phone || [];
                            contact.phone.push(value);
                        }
                        if (/EMAIL/.test(key)) contact.email = value;
                        if (/ADR/.test(key)) contact.address = value.replace(/;/g, ' ');
                        if (/NOTE/.test(key)) contact.note = value;
                        if (/ORG/.test(key)) contact.org = value;
                        if (/BDAY/.test(key)) contact.birthday = value;
                        if (/URL/.test(key)) contact.url = value;
                    }
                });

                return contact;
            }).filter(contact => contact.name || contact.phone); // فلترة جهات الاتصال الفارغة
        }

        function displayContacts(contacts) {
            contactsContainer.innerHTML = '';
            updateStats(contacts.length);

            if (viewMode === 'cards') {
                contactsContainer.className = 'grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 w-full max-w-6xl';
                contacts.forEach(contact => {
                    const card = document.createElement('div');
                    card.className = 'glass p-4 shadow-lg';
                    card.innerHTML = `
            <h2 class="text-xl font-bold mb-2">${contact.name || 'بدون اسم'}</h2>
            ${contact.phone ? contact.phone.map(p => `<p>📞 ${p} <button onclick=\"copyToClipboard('${p}')\" class='ml-2 text-sm underline'>نسخ</button></p>`).join('') : ''}
            ${contact.email ? `<p>📧 ${contact.email}</p>` : ''}
            ${contact.address ? `<p>📍 ${contact.address}</p>` : ''}
            ${contact.org ? `<p>🏢 ${contact.org}</p>` : ''}
            ${contact.birthday ? `<p>🎂 ${contact.birthday}</p>` : ''}
            ${contact.url ? `<p>🔗 <a href="${contact.url}" target="_blank" class="underline">رابط</a></p>` : ''}
            ${contact.note ? `<p>📝 ${contact.note}</p>` : ''}
            <button onclick="showQR('${encodeURIComponent(contact.name || '')}\n${(contact.phone || []).join(', ')}')" class="mt-2 bg-indigo-500 text-white px-3 py-1 rounded">📤 QR</button>
          `;
                    contactsContainer.appendChild(card);
                });
            } else {
                contactsContainer.className = 'w-full max-w-6xl overflow-auto';
                const table = document.createElement('table');
                table.className = 'w-full table-auto bg-white text-black';
                table.innerHTML = `<thead><tr class='bg-gray-300'>
          <th class='p-2'>الاسم</th><th>الهاتف</th><th>البريد</th><th>الشركة</th><th>QR</th>
        </tr></thead><tbody>
        ${contacts.map(c => `
          <tr class='border'>
            <td class='p-2'>${c.name || ''}</td>
            <td>${(c.phone || []).join(', ')}</td>
            <td>${c.email || ''}</td>
            <td>${c.org || ''}</td>
            <td><button onclick="showQR('${encodeURIComponent(c.name || '')}\n${(c.phone || []).join(', ')}')" class="bg-blue-600 text-white px-2 py-1 rounded">QR</button></td>
          </tr>`).join('')}
        </tbody>`;
                contactsContainer.appendChild(table);
            }
        }

        function filterContacts(e) {
            const query = e.target.value.toLowerCase();
            const filtered = allContacts.filter(c => {
                return (c.name && c.name.toLowerCase().includes(query)) ||
                    (c.phone && c.phone.join(',').includes(query)) ||
                    (c.email && c.email.toLowerCase().includes(query));
            });
            displayContacts(filtered);
        }

        function exportToCSV() {
            if (allContacts.length === 0) {
                showNotification('لا يوجد جهات اتصال لتصديرها', 'error');
                return;
            }

            const headers = ['الاسم', 'الهاتف', 'البريد', 'العنوان', 'الشركة', 'الملاحظة', 'تاريخ الميلاد', 'الرابط'];
            const rows = allContacts.map(c => [
                c.name || '',
                (c.phone || []).join(' | '),
                c.email || '',
                c.address || '',
                c.org || '',
                c.note || '',
                c.birthday || '',
                c.url || ''
            ]);
            const csv = [headers.join(','), ...rows.map(r => r.map(x => `"${x}"`).join(','))].join('\n');
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'contacts.csv';
            link.click();
            showNotification(`تم تصدير ${allContacts.length} جهة اتصال إلى CSV`, 'success');
        }

        let currentQRCanvas = null;
        let currentQRData = '';

        function showQR(data) {
            currentQRData = decodeURIComponent(data);
            qrModal.classList.remove('hidden');
            qrCodeContainer.innerHTML = '';

            QRCode.toCanvas(document.createElement('canvas'), currentQRData, {
                width: 200,
                margin: 2,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            }, (err, canvas) => {
                if (!err) {
                    currentQRCanvas = canvas;
                    qrCodeContainer.appendChild(canvas);

                    // إضافة event listeners لأزرار التحميل والمشاركة
                    document.getElementById('downloadQR').onclick = downloadQR;
                    document.getElementById('shareQR').onclick = shareQR;
                }
            });
        }

        function downloadQR() {
            if (!currentQRCanvas) return;

            const link = document.createElement('a');
            link.download = 'contact-qr.png';
            link.href = currentQRCanvas.toDataURL();
            link.click();
            showNotification('تم تحميل رمز QR بنجاح', 'success');
        }

        function shareQR() {
            if (!currentQRData) return;

            if (navigator.share) {
                // استخدام Web Share API إذا كان متاحاً
                navigator.share({
                    title: 'جهة اتصال',
                    text: currentQRData,
                }).then(() => {
                    showNotification('تم مشاركة جهة الاتصال بنجاح', 'success');
                }).catch(err => {
                    console.log('خطأ في المشاركة:', err);
                    shareQRFallback();
                });
            } else {
                shareQRFallback();
            }
        }

        function shareQRFallback() {
            // نسخ البيانات إلى الحافظة
            navigator.clipboard.writeText(currentQRData).then(() => {
                showNotification('تم نسخ بيانات جهة الاتصال إلى الحافظة', 'success');
            }).catch(() => {
                // إنشاء رابط مؤقت للمشاركة
                const shareUrl = `data:text/plain;charset=utf-8,${encodeURIComponent(currentQRData)}`;
                const link = document.createElement('a');
                link.href = shareUrl;
                link.download = 'contact.txt';
                link.click();
                showNotification('تم تحميل ملف جهة الاتصال', 'info');
            });
        }

        // تحميل آخر جلسة
        window.addEventListener('DOMContentLoaded', () => {
            const last = localStorage.getItem('lastVCF');
            if (last) {
                allContacts = parseVCF(last);
                displayContacts(allContacts);
            }
        });
    </script>
</body>

</html>